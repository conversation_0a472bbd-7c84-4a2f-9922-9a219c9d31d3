// TypeScript IntelliSense for VITE_ .env variables.
// VITE_ prefixed variables are exposed to the client while non-VITE_ variables aren't
// https://vitejs.dev/guide/env-and-mode.html

/// <reference types="vite/client" />

interface ImportMetaEnv {
	readonly VITE_APP_TITLE: string;
	readonly VITE_API_URL: string;
	readonly VITE_FIREBASE_API_KEY: string;
	readonly VITE_FIREBASE_AUTH_DOMAIN: string;
	readonly VITE_FIREBASE_PROJECT_ID: string;
	readonly VITE_FIREBASE_STORAGE_BUCKET: string;
	readonly VITE_FIREBASE_MESSAGING_SENDER_ID: string;
	readonly VITE_FIREBASE_ID: string;
	readonly VITE_FIREBASE_MEASUREMENT_ID: string;

	readonly VITE_AWS_ACCESS_KEY: string;
	readonly VITE_AWS_SECRET_KEY: string;

	// PostHog environment variables
	readonly VITE_PUBLIC_POSTHOG_KEY: string;
	readonly VITE_PUBLIC_POSTHOG_HOST: string;

	// Chatbot environment variables
	readonly VITE_CHATBOT_URL: string;
	readonly VITE_CHATBOT_FALLBACK_URL: string;

	// more env variables...
}

interface ImportMeta {
	readonly env: ImportMetaEnv;
}

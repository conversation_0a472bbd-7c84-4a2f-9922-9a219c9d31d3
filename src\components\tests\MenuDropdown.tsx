import React, { useEffect, useRef, useState, useCallback } from "react";
import { ChevronDown } from "lucide-react";

export type MenuDropdownBaseProps = {
	value: string;
	onChange: (value: string) => void;
	options: string[];
	placeholder: string;
	menuMinWidth?: string;

	formatLabel?: (value: string) => string;

	open?: boolean;
	onOpenChange?: (open: boolean) => void;

	className?: string;
};

const BaseDropdown: React.FC<MenuDropdownBaseProps> = ({
	value,
	onChange,
	options,
	placeholder,
	menuMinWidth = "12rem",
	formatLabel,
	open,
	onOpenChange,
	className = "",
}) => {
	const isControlled = open !== undefined;
	const [internalOpen, setInternalOpen] = useState(false);
	const actualOpen = isControlled ? !!open : internalOpen;

	const setActualOpen = (next: boolean) => {
		onOpenChange?.(next);
		if (!isControlled) setInternalOpen(next);
	};

	const ref = useRef<HTMLDivElement>(null);

	const renderLabel = useCallback(
		(v: string) => (formatLabel ? formatLabel(v) : v),
		[formatLabel]
	);

	useEffect(() => {
		const onOutsidePointerDown = (e: PointerEvent) => {
			if (!actualOpen) return;
			const target = e.target as Node | null;
			if (ref.current && target && ref.current.contains(target)) return;
			setActualOpen(false);
		};
		document.addEventListener("pointerdown", onOutsidePointerDown);
		return () =>
			document.removeEventListener("pointerdown", onOutsidePointerDown);
	}, [actualOpen]);

	useEffect(() => {
		const onKey = (e: KeyboardEvent) => {
			if (e.key === "Escape") setActualOpen(false);
		};
		document.addEventListener("keydown", onKey);
		return () => document.removeEventListener("keydown", onKey);
	}, []);

	const visibleOptions = options;

	return (
		<div className={`relative ${className}`} ref={ref}>
			<button
				type="button"
				onPointerDown={(e) => e.stopPropagation()}
				onMouseDown={(e) => e.stopPropagation()}
				onClick={(e) => {
					e.stopPropagation();
					setActualOpen(!actualOpen);
				}}
				className="flex items-center gap-1 w-full px-0 py-0 bg-transparent focus:outline-none"
				style={{ fontFamily: "Inter, sans-serif", fontWeight: 500 }}
				aria-haspopup="listbox"
				aria-expanded={actualOpen}
			>
				<span className={value ? "text-gray-800" : "text-gray-500"}>
					{value ? renderLabel(value) : placeholder}
				</span>
				<ChevronDown
					className={`w-4 h-4 text-gray-500 transition-transform ${
						actualOpen ? "rotate-180" : ""
					}`}
				/>
			</button>

			{actualOpen && (
				<div
					className="absolute z-[60] w-max mt-2 bg-white border border-gray-200 rounded-md shadow-lg"
					style={{ minWidth: menuMinWidth }}
					role="listbox"
					tabIndex={-1}
					onPointerDown={(e) => e.stopPropagation()}
					onMouseDown={(e) => e.stopPropagation()}
					onClick={(e) => e.stopPropagation()}
				>
					{visibleOptions.length === 0 ? (
						<div
							className="px-3 py-2 text-sm text-gray-500"
							style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
						>
							No options
						</div>
					) : (
						visibleOptions.map((option) => (
							<button
								key={option}
								type="button"
								onPointerDown={(e) => e.stopPropagation()}
								onMouseDown={(e) => e.stopPropagation()}
								onClick={(e) => {
									e.stopPropagation();
									onChange(option);
									setActualOpen(false);
								}}
								className="block w-full px-3 py-2 text-left text-sm hover:bg-gray-50 first:rounded-t-md last:rounded-b-md"
								style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
								role="option"
								aria-selected={option === value}
							>
								{renderLabel(option)}
							</button>
						))
					)}
				</div>
			)}
		</div>
	);
};

export type MenuDropdownProps = Omit<
	MenuDropdownBaseProps,
	"placeholder" | "menuMinWidth"
> & {
	placeholder: string;
	menuMinWidth?: string;
};

export const MenuDropdown: React.FC<MenuDropdownProps> = ({
	value,
	onChange,
	options,
	placeholder,
	menuMinWidth = "12rem",
	className = "",
	open,
	onOpenChange,
	formatLabel,
}) => (
	<div
		className={`flex-shrink-0 text-sm font-inter font-medium text-gray-700 ${className}`}
	>
		<BaseDropdown
			value={value}
			onChange={onChange}
			options={options}
			placeholder={placeholder}
			menuMinWidth={menuMinWidth}
			open={open}
			onOpenChange={onOpenChange}
			formatLabel={formatLabel}
		/>
	</div>
);

type SimpleSelectProps = Omit<
	MenuDropdownBaseProps,
	"placeholder" | "menuMinWidth"
> & {
	open?: boolean;
	onOpenChange?: (open: boolean) => void;
	className?: string;
};

// Map backend codes
const typeLabel = (v: string) => {
	const key = v?.toLowerCase?.() ?? v;
	if (key === "roha") return "cramming";
	return v;
};
export const MCQTypeDropdown: React.FC<SimpleSelectProps> = ({
	value,
	onChange,
	options,
	open,
	onOpenChange,
	className = "",
}) => (
	<div
		className={`w-48 flex-shrink-0 text-sm font-inter font-medium text-gray-700 ${className}`}
	>
		<BaseDropdown
			value={value}
			onChange={onChange}
			options={options}
			placeholder={`MCQ's Type`}
			menuMinWidth="14rem"
			open={open}
			onOpenChange={onOpenChange}
			formatLabel={typeLabel}
		/>
	</div>
);

export const DifficultyDropdown: React.FC<SimpleSelectProps> = ({
	value,
	onChange,
	options,
	open,
	onOpenChange,
	className = "",
}) => (
	<div
		className={`w-40 flex-shrink-0 text-sm font-inter font-medium text-gray-700 ${className}`}
	>
		<BaseDropdown
			value={value}
			onChange={onChange}
			options={options}
			placeholder="Difficulty"
			menuMinWidth="12rem"
			open={open}
			onOpenChange={onOpenChange}
		/>
	</div>
);

export default BaseDropdown;

import * as React from "react";
import { Search } from "lucide-react";
import { cn } from "@/lib/utils";

type TopicsSearchBarProps = {
	value: string;
	onChange: (value: string) => void;

	placeholder: string;

	className?: string;
	inputClassName?: string;
	autoFocus?: boolean;
};

const TopicsSearchBar: React.FC<TopicsSearchBarProps> = ({
	value,
	onChange,
	placeholder,
	className = "",
	inputClassName,
	autoFocus = false,
}) => {
	return (
		<div
			className={cn(
				"relative w-full h-[45px] sm:w-[456px] sm:h-[55px]",
				className
			)}
		>
			<Search
				className={cn(
					"pointer-events-none absolute top-1/2 -translate-y-1/2 text-gray-400",
					"left-3 w-4 h-4",
					"sm:left-4 sm:w-5 sm:h-5"
				)}
			/>

			<input
				type="text"
				value={value}
				onChange={(e) => onChange(e.target.value)}
				placeholder={placeholder}
				autoFocus={autoFocus}
				className={cn(
					`
          w-full h-full
          border border-gray-300 rounded-xl
  
          pl-10 pr-3 py-2 text-sm
          
          sm:pl-12 sm:pr-[14px] sm:py-[27px]
          focus:outline-none focus:ring-2 focus:ring-blue-500
        `,
					inputClassName
				)}
				style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
			/>
		</div>
	);
};

export default TopicsSearchBar;

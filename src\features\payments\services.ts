import { api } from "@/lib/api";
import type { APIResponse } from "@/lib/api/types";
import type {
	ApplyDiscountPayload,
	CreateOrderPayload,
	DiscountInfo,
	Order,
	PaymentProofUploadUrl,
} from "./types";

export const createOrder = (data: CreateOrderPayload) =>
	api.post<APIResponse<Order>>("/payments/create-order/", data);

export const applyDiscount = (data: ApplyDiscountPayload) =>
	api.post<APIResponse<DiscountInfo>>(
		`/payments/order/${data.payment_id}/apply-discount/`,
		data
	);

export const getPaymentProofUploadUrl = (paymentId: string) =>
	api.get<APIResponse<PaymentProofUploadUrl>>(
		`/payments/proof/upload-url/${paymentId}`
	);

export const getOrder = (paymentId: string) =>
	api.get<APIResponse<Order>>(`/payments/order/${paymentId}`);

export const uploadPaymentProof = async (file: File, uploadUrl: string) => {
	// Simple PUT request without modifying headers (which can invalidate presigned URL)
	const response = await fetch(uploadUrl, {
		method: "PUT",
		body: file,
		// Only set Content-Type, as this is typically allowed in the presigned URL
		headers: {
			"Content-Type": file.type,
		},
	});

	console.log("Upload response:", response);

	if (!response.ok) {
		throw new Error(
			`Failed to upload file: ${response.status} ${response.statusText}`
		);
	}

	return true;
};

{"name": "parhlai-frontend-core", "author": "aa-del9", "description": "Frontend Core for Parhlai - in React", "license": "MIT", "version": "1.0.0", "type": "module", "scripts": {"setup": "git init", "format": "prettier \"src/**/*.{ts,tsx}\" --write", "lint": "eslint --max-warnings 0", "lint:fix": "eslint --max-warnings 0 --fix", "dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "generate": "tsr generate", "watch-routes": "tsr watch"}, "dependencies": {"@fontsource/inter": "^5.1.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.6", "@stepperize/react": "^3.1.1", "@tanstack/react-query": "^5.59.16", "@tanstack/react-router": "^1.76.1", "axios": "^1.7.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "0.2.1", "firebase": "^11.0.2", "immer": "^10.1.1", "lucide-react": "^0.541.0", "posthog-js": "^1.259.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-hook-form": "^7.53.2", "react-latex-next": "^3.0.0", "react-phone-number-input": "^3.4.9", "react-responsive": "^10.0.1", "react-scroll": "^1.9.3", "react-youtube": "^10.1.0", "recharts": "^3.0.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/compat": "^1.1.1", "@eslint/js": "^9.16.0", "@faker-js/faker": "^8.4.1", "@hookform/devtools": "^4.3.1", "@tanstack/react-query-devtools": "^5.53.1", "@tanstack/router-cli": "^1.79.0", "@tanstack/router-devtools": "^1.51.6", "@tanstack/router-plugin": "^1.51.6", "@total-typescript/ts-reset": "^0.6.0", "@types/eslint__js": "^8.42.3", "@types/node": "^22.5.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-scroll": "^1", "@typescript-eslint/eslint-plugin": "^8.3.0", "@typescript-eslint/parser": "^8.3.0", "@vitejs/plugin-react-swc": "^3.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-refresh": "^0.4.11", "eslint-plugin-unicorn": "^55.0.0", "globals": "^15.12.0", "jsdom": "^25.0.0", "postcss": "^8.4.41", "prettier": "^3.3.3", "prop-types": "^15.8.1", "tailwindcss": "^3.4.10", "typescript": "^5.5.4", "typescript-eslint": "^8.16.0", "vite": "5.4.2", "vite-plugin-node-polyfills": "^0.22.0", "vite-plugin-static-copy": "^1.0.6"}, "packageManager": "yarn@4.5.1"}
import { api } from "@/lib/api";
import type {
	GetResourcesRes,
	GetFiltersRes,
	FilterParams,
	GetResourceByIdRes,
} from "./types";

export const getResources = (
	subject: string,
	resourceType: string,
	searchTerm?: string,
	filters?: FilterParams,
	page: number = 1,
	limit: number = 10
) => {
	// Create params object with pagination
	const params: Record<string, string | number | string[]> = {
		page,
		limit,
	};

	// Add search parameter if provided
	if (searchTerm && searchTerm.trim()) {
		// Replace spaces with + to match backend URL encoding expectations
		params["search"] = searchTerm.trim().replace(/\s+/g, "+");
	}

	// Add filter parameters if provided
	if (filters) {
		if (filters.entryTests && filters.entryTests.length > 0) {
			params["entryTest"] = filters.entryTests;
		}
		if (filters.chapters && filters.chapters.length > 0) {
			params["chapter"] = filters.chapters;
		}
		if (filters.topics && filters.topics.length > 0) {
			params["topic"] = filters.topics;
		}
		if (filters.language && filters.language.length > 0) {
			params["lang"] = filters.language;
		}
		if (filters.categories && filters.categories.length > 0) {
			params["category"] = filters.categories;
		}
	}

	return api.get<GetResourcesRes>(`resources/get/${subject}/${resourceType}`, {
		params,
	});
};

export const getFilters = (
	subject: string,
	resourceType: string,
	chapters?: string[]
) => {
	// Create params object for filter API
	const params: Record<string, string | string[]> = {};

	// Add chapter filter parameter if provided (for filtering the filter options)
	if (chapters && chapters.length > 0) {
		params["chapter"] = chapters;
	}

	return api.get<GetFiltersRes>(
		`resources/filters/${subject}/${resourceType}`,
		{
			params,
		}
	);
};

export const getResourceById = (resourceId: string) => {
	return api.get<GetResourceByIdRes>(`resources/${resourceId}`);
};

import { But<PERSON> } from "@/components/ui/button";
import { IMAGES } from "@/lib/assets/images";
import { ChevronRight } from "react-feather";

export const UpgradePlan = () => {
	return (
		<div className="flex flex-row items-center p-4 gap-3 w-full h-[100px] border-white/15 lg:relative lg:w-[170px] lg:h-[210px] lg:bg-primary/10 lg:rounded-[19px]">
			<div className="flex-none">
				<img
					src={IMAGES.prolock}
					alt="pro lock"
					className="w-[70px] h-[70px] lg:absolute lg:-top-8 lg:left-1/2 lg:-translate-x-1/2 lg:w-[140px] lg:h-[140px]"
				/>
			</div>
			<div className="flex-grow lg:absolute lg:top-[125px] lg:left-1/2 lg:-translate-x-1/2 lg:w-[140px]">
				<p className="text-white text-sm lg:text-center lg:text-foreground lg:text-sm lg:leading-tight">
					Upgrade to <span className="font-bold lg:text-primary">PRO</span> for
					more features.
				</p>
			</div>
			<div className="flex-none">
				<Button className="hidden lg:block absolute lg:top-[175px] lg:left-1/2 lg:-translate-x-1/2 w-[160px] h-[40px] bg-gradient-to-b from-white/20 to-transparent shadow-[0px_1px_2px_rgba(89,54,205,0.48),_0px_0px_0px_1px_#5936CD] rounded-[10px]">
					Upgrade Plan
				</Button>
				<ChevronRight className="lg:hidden" />
			</div>
		</div>
	);
};

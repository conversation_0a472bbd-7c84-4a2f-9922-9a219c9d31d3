import { AnalyticsData, CHART_COLORS } from './types';

// Utility functions
export const formatName = (name: string): string => {
	return name === name.toUpperCase() 
		? name 
		: name.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());
};

export const calculateCorrectWrongFromSubjects = (
	subjectDivision: Record<string, any>, 
	type: string, 
	category: 'type' | 'difficulty'
): { correct: number; wrong: number } => {
	let correct = 0;
	let wrong = 0;
	
	Object.values(subjectDivision || {}).forEach((subjectData: any) => {
		const categoryData = subjectData?.[category]?.[type];
		if (categoryData) {
			correct += categoryData.correct || 0;
			wrong += categoryData.wrong || 0;
		}
	});
	
	return { correct, wrong };
};

export const transformDataToAnalytics = (
	data: Record<string, number>, 
	subjectDivision: Record<string, any>, 
	category: 'type' | 'difficulty'
): AnalyticsData[] => {
	return Object.entries(data || {}).map(([key, value]) => {
		const { correct, wrong } = calculateCorrectWrongFromSubjects(subjectDivision, key, category);
		return {
			name: key,
			total: value,
			correct: { value: correct, fill: CHART_COLORS.correct },
			wrong: { value: wrong, fill: CHART_COLORS.wrong },
		};
	});
};

export const transformSubjectData = (data: Record<string, any>): AnalyticsData[] => {
	return Object.entries(data || {}).map(([key, value]: [string, any]) => ({
		name: key,
		total: (value.correct || 0) + (value.wrong || 0),
		correct: { value: value.correct || 0, fill: CHART_COLORS.correct },
		wrong: { value: value.wrong || 0, fill: CHART_COLORS.wrong },
	}));
};

// Transform subjectDivision data to extract subjects data
export const extractSubjectsFromDivision = (subjectDivision: Record<string, any>): Record<string, { correct: number; wrong: number }> => {
	const subjects: Record<string, { correct: number; wrong: number }> = {};

	Object.entries(subjectDivision || {}).forEach(([subjectName, subjectData]) => {
		let totalCorrect = 0;
		let totalWrong = 0;

		// Sum up correct and wrong answers from difficulty breakdown
		Object.values(subjectData?.difficulty || {}).forEach((diffData: any) => {
			totalCorrect += diffData.correct || 0;
			totalWrong += diffData.wrong || 0;
		});

		subjects[subjectName] = {
			correct: totalCorrect,
			wrong: totalWrong
		};
	});

	return subjects;
};

// Transform subjectDivision data to extract mcqsType data
export const extractMcqsTypeFromDivision = (subjectDivision: Record<string, any>): Record<string, { correct: number; wrong: number }> => {
	const mcqsType: Record<string, { correct: number; wrong: number }> = {};

	Object.values(subjectDivision || {}).forEach((subjectData: any) => {
		Object.entries(subjectData?.type || {}).forEach(([typeName, typeData]: [string, any]) => {
			if (!mcqsType[typeName]) {
				mcqsType[typeName] = { correct: 0, wrong: 0 };
			}
			mcqsType[typeName].correct += typeData.correct || 0;
			mcqsType[typeName].wrong += typeData.wrong || 0;
		});
	});

	return mcqsType;
};

// Transform subjectDivision data to extract difficultyType data
export const extractDifficultyTypeFromDivision = (subjectDivision: Record<string, any>): Record<string, { correct: number; wrong: number }> => {
	const difficultyType: Record<string, { correct: number; wrong: number }> = {};

	Object.values(subjectDivision || {}).forEach((subjectData: any) => {
		Object.entries(subjectData?.difficulty || {}).forEach(([difficultyName, difficultyData]: [string, any]) => {
			if (!difficultyType[difficultyName]) {
				difficultyType[difficultyName] = { correct: 0, wrong: 0 };
			}
			difficultyType[difficultyName].correct += difficultyData.correct || 0;
			difficultyType[difficultyName].wrong += difficultyData.wrong || 0;
		});
	});

	return difficultyType;
};

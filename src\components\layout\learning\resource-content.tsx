import { VideoCard } from "@/components/layout/learning/video-card";
import { FileCard } from "@/components/layout/learning/file-card";
import { LinkCard } from "@/components/layout/learning/link-card";
import { ResourceType } from "@/features/learning/types";
import { Resource } from "@/features/resources/types";

type ResourceContentProps = {
	type: ResourceType["id"];
	resources: Resource[];
};

const ResourceContent = ({ type, resources }: ResourceContentProps) => {
	if (!resources || resources.length === 0) {
		return <p className="text-sm text-gray-500">No resources available.</p>;
	}
	switch (type) {
		case "video":
			return (
				<div className="grid sm:grid-cols-2 gap-3 xl:px-3">
					{resources.map((resource) => (
						<VideoCard key={resource._id} resource={resource} />
					))}
				</div>
			);
		case "file":
			return (
				<div className="space-y-2 lg:px-3">
					{resources.map((resource) => (
						<FileCard key={resource.id} resource={resource} />
					))}
				</div>
			);
		case "link":
			return (
				<div className="space-y-2 lg:px-3">
					{resources.map((resource) => (
						<LinkCard key={resource.id} resource={resource} />
					))}
				</div>
			);
		default:
			return null;
	}
};

export default ResourceContent;

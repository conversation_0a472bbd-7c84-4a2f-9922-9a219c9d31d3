import React from "react";
import { Link as RouterLink } from "@tanstack/react-router";
import { ArrowRight } from "react-feather";

interface GrokKeyBannerProps {
	hasGrokKey: boolean;
	className?: string;
}

const GrokKeyBanner: React.FC<GrokKeyBannerProps> = ({ hasGrok<PERSON>ey, className = "" }) => {
	if (hasGrok<PERSON>ey) {
		return (
			<div className={`w-[268px] h-[91px] bg-gradient-to-r from-[#972DF4] to-[#5936CD] rounded-[24px] shadow-[6px_6px_54px_rgba(0,0,0,0.05)] flex flex-row justify-end items-center px-5 py-[15px] gap-1 text-white ${className}`}>
				{/* Image placeholder */}
				<div className="w-[61px] h-[61px] bg-white/20 rounded-[10px] flex items-center justify-center flex-shrink-0">
					<span className="text-4xl">🤖</span>
				</div>
				{/* Text content */}
				<div className="w-[168px] h-[61px] flex flex-col items-end">
					<h3 className="font-semibold text-base leading-5 text-right">You have Unlimited</h3>
					<p className="text-purple-100 text-sm text-right">Private Chatbot!</p>
					<RouterLink
						to="/add-key"
						className="inline-flex items-center gap-1 text-xs underline hover:no-underline mt-1"
					>
						<span>Change Grok Key</span>
						<ArrowRight size={12} />
					</RouterLink>
				</div>
			</div>
		);
	}

	return (
		<div className={`w-[318px] h-[95px] bg-white rounded-[24px] shadow-[6px_6px_54px_rgba(0,0,0,0.05)] flex flex-row justify-end items-center px-5 py-[15px] gap-1 ${className}`}>
			{/* Image placeholder */}
			<div className="w-[65px] h-[65px] bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
				<span className="text-4xl">🤖</span>
			</div>
			{/* Text content */}
			<div className="w-[202px] h-[61px] flex flex-col items-end">
				<h3 className="font-semibold text-base leading-5 text-[#202224] text-right">Want unlimited Chatbot?</h3>
				<p className="text-sm text-gray-600 text-right">Bring your own key!</p>
				<RouterLink
					to="/add-key"
					className="inline-flex items-center gap-1 text-purple-600 text-xs underline hover:no-underline mt-1"
				>
					<span>Add Grok Key</span>
					<ArrowRight size={12} />
				</RouterLink>
			</div>
		</div>
	);
};

export default GrokKeyBanner;

export interface ChatbotStatusResponse {
	message: string;
	uid: string;
	email: string;
	has_groq_api_key: boolean;
}

export interface AddGrokKeyRequest {
	groq_api_key: string;
}

export interface AddGrokKeyResponse {
	success: boolean;
	message: string;
}

export interface MCQChatbotRequest {
	mcqid: string;
	mcqTitle: string;
	options: string[];
	userChoice: string;
	correctAnswer: string;
	explanation: string;
	question: string;
}

export interface MCQChatbotResponse {
	result: {
		id: string;
		object: string;
		created: number;
		model: string;
		choices: Array<{
			index: number;
			message: {
				role: string;
				content: string;
			};
			logprobs: null;
			finish_reason: string;
		}>;
		usage: {
			queue_time: number;
			prompt_tokens: number;
			prompt_time: number;
			completion_tokens: number;
			completion_time: number;
			total_tokens: number;
			total_time: number;
		};
		usage_breakdown: null;
		system_fingerprint: string;
		x_groq: {
			id: string;
		};
		service_tier: string;
	};
	mcqid: string;
}

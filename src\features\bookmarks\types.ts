

export type getBookmarksReq<C> = {
	category: C;
	page: number;
	limit: number;
};

type Pagination = {
	currentPage: number;
	limit: number;
	totalItems: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPrevPage: boolean;
};

type Count = {
	resources: number;
	mcqs: number;
	quizzes: number;
	currentPageTotal: number;
};

type BaseApiResponse<T> = {
	status: number;
	success: boolean;
	message: string;
	data: T & {
		pagination: Pagination;
		count: Count;
	};
};

type ResourceData = {
	resources: any[];
	totalResources: number;
};

type McqData = {
	mcqs: any[];
	totalMcqs: number;
};

type QuizData = {
	quizzes: any[];
	totalQuizzes: number;
};

export type getBookmarksResp = {
	quiz: BaseApiResponse<QuizData>;
	mcq: BaseApiResponse<McqData>;
	resource: BaseApiResponse<ResourceData>;
};

export type addBookmarkReq = {
	category: "resource" | "mcq" | "quiz";
	id: string;
};

import { useMemo, useState } from "react";
import { ArrowRight, Check } from "react-feather";
import { Link } from "@tanstack/react-router";
import CheckoutPage from "./checkout";
import { ICONS } from "@/lib/assets/images";
import { calculatePrice } from "@/features/pricing/utils";

type PlanType = "daily" | "subscription" | "custom";

type PlanInfo = {
	type: PlanType;
	price: number;
	days?: number;
};

const PURPLE = "#5936CD";

type PlanCardProps = {
	title: string;
	price: string;
	period: string;
	onClick: () => void;
	bgColor?: string;
	textColor?: string;
};

type CustomPlanProps = {
	onSelect: (days: number, price: number) => void;
};

export default function PricingPage() {
	const [showCheckout, setShowCheckout] = useState(false);
	const [selectedPlan, setSelectedPlan] = useState<PlanInfo | null>(null);

	const handleSelectPlan = (
		planType: PlanType,
		price: number,
		days?: number
	) => {
		setSelectedPlan({ type: planType, price, days });
		setShowCheckout(true);
	};

	const handleBackToPricing = () => setShowCheckout(false);

	if (showCheckout && selectedPlan) {
		return (
			<CheckoutPage
				planType={selectedPlan.type}
				price={selectedPlan.price}
				days={selectedPlan.days}
				onBack={handleBackToPricing}
			/>
		);
	}

	return (
		<div
			className='min-h-screen bg-[#F9FCFF] font-["Inter",_sans-serif] p-4 sm:p-6 lg:p-8'
			style={{
				background:
					"linear-gradient(105.27deg, #CBBBFF 16.17%, rgba(255, 255, 255, 0) 87.53%)",
			}}
		>
			<div className="max-w-7xl mx-auto">
				<header className="flex items-center justify-between">
					<img
						src={ICONS.logoexpanded}
						alt="Logo"
						className="w-32 sm:w-36 lg:w-40"
					/>
				</header>

				<div className="text-center my-8 lg:my-12">
					<h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tighter text-black">
						Ready to ace your exams?
					</h1>
					<p className="mt-3 text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
						All exams. One subscription.
					</p>
				</div>

				<main className="flex flex-col lg:flex-row gap-8 justify-center">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<PlanCard
							title="Daily Plan"
							price="99"
							period="/day"
							onClick={() => handleSelectPlan("daily", 99)}
						/>
						<PlanCard
							title="Bi-Monthly Plan"
							price="2000"
							period="/Two months"
							bgColor="bg-[#7B53FF]"
							textColor="text-white"
							onClick={() => handleSelectPlan("subscription", 2000)}
						/>
						<PlanCard
							title="Monthly plan"
							price="1000"
							period="/month"
							bgColor="bg-[#5936CD]"
							textColor="text-white"
							onClick={() => handleSelectPlan("subscription", 1000)}
						/>
						<CustomPlan
							onSelect={(days, price) =>
								handleSelectPlan("custom", price, days)
							}
						/>
					</div>

					<aside
						className="lg:w-[360px] rounded-[50px] p-8 lg:sticky top-8 h-fit"
						style={{
							background: "rgba(255, 255, 255, 0.05)",
							boxShadow:
								"-15px -15px 4px -15px rgba(255, 255, 255, 0.5), 0px 0px 50px rgba(0, 0, 0, 0.15), inset -15px 15px 4px -15px rgba(255, 255, 255, 0.5), inset 0px 4px 15px rgba(255, 255, 255, 0.5)",
							backdropFilter: "blur(15px)",
						}}
					>
						<h4 className="font-bold text-2xl text-center text-black">
							Features Included
						</h4>
						<ul className="mt-8 space-y-4">
							{[
								"Unlimited mock tests",
								"Custom quiz builder",
								"All Entry Tests",
								"50k+ question bank",
								"1k+ resources bank",
								"AI based analytics",
								"AI Tutorbot",
								"No extra bundles or hidden charges",
								"All services in one plan",
							].map((service, i) => (
								<li key={i} className="flex items-center gap-3">
									<Check size={24} color={PURPLE} />
									<span className="font-medium text-lg text-[#334155]">
										{service}
									</span>
								</li>
							))}
						</ul>
					</aside>
				</main>

				<footer className="text-center mt-12">
					<Link
						className="flex items-center justify-center gap-2 font-medium text-lg text-[#5936CD]"
						to="/dashboard"
					>
						No thanks, I'll try it out first.
						<ArrowRight size={20} />
					</Link>
				</footer>
			</div>
		</div>
	);
}

function PlanCard({
	title,
	price,
	period,
	onClick,
	bgColor = "bg-white",
	textColor = "text-black",
}: PlanCardProps) {
	return (
		<div
			className={`shadow-[-4px_4px_30px_rgba(0,0,0,0.04)] rounded-[18px] p-5 flex flex-col ${bgColor} ${textColor} min-h-[280px]`}
		>
			<h3 className="font-bold text-2xl">{title}</h3>
			<div className="flex items-end gap-1 mt-4">
				<span className="font-bold text-4xl">Rs. {price}</span>
				<span
					className={`font-semibold text-base ${textColor === "text-white" ? "opacity-40" : "text-[#656565]"}`}
				>
					{period}
				</span>
			</div>
			<button
				onClick={onClick}
				className={`w-full h-12 mt-auto rounded-[10px] font-semibold text-sm ${textColor === "text-white" ? "bg-white text-[#1A1C1E]" : "bg-[#5936CD] text-white"}`}
				style={{
					boxShadow:
						textColor === "text-white"
							? "inset 0px -3px 6px rgba(244, 245, 250, 0.6)"
							: "0px 1px 2px rgba(89, 54, 205, 0.48), 0px 0px 0px 1px #5936CD",
				}}
			>
				Get
			</button>
		</div>
	);
}

function CustomPlan({ onSelect }: CustomPlanProps) {
	const [days, setDays] = useState(365);
	const { total: price } = useMemo(() => calculatePrice(days), [days]);

	return (
		<div className="bg-gradient-to-b from-[#5936CD] to-[#2D1B67] shadow-[-4px_4px_30px_rgba(0,0,0,0.04)] rounded-[18px] p-5 flex flex-col text-white min-h-[280px]">
			<h3 className="font-bold text-2xl">Custom Plan</h3>
			<div className="mt-8">
				<input
					type="range"
					min={1}
					max={365}
					value={days}
					onChange={(e) => setDays(parseInt(e.target.value))}
					className="custom-slider"
				/>
			</div>
			<div className="flex items-end gap-1 mt-4">
				<span className="font-bold text-4xl">
					Rs. {price.toString().padStart(4, "0")}
				</span>
				<span className="font-semibold text-base opacity-40">/{days} Days</span>
			</div>
			<button
				onClick={() => onSelect(days, price)}
				className="w-full h-12 mt-auto bg-white rounded-[10px] text-[#1A1C1E] font-semibold text-sm"
				style={{
					border: "1px solid #EFF0F6",
					boxShadow: "inset 0px -3px 6px rgba(244, 245, 250, 0.6)",
				}}
			>
				Buy
			</button>
		</div>
	);
}

import { ArrowLeft } from "react-feather";
import { useEffect } from "react";
import {
	createFileRoute,
	useNavigate,
	getRoute<PERSON>pi,
} from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import { ICONS } from "@/lib/assets/images";
import { useAuthStore } from "@/features/auth/store";
import { getOrder } from "@/features/payments/services";

export type PaymentSuccessSearch = {
	paymentId?: string; // required for access
	plan?: string;
	amount?: number;
	discount?: number;
	total?: number;
	orderId?: string;
	currency?: string;
	ts?: string;
};

/** Register the route with the EXACT literal your app uses */
export const Route = createFileRoute("/(app)/_pricing/payment-success")({
	component: PaymentSuccess,
	validateSearch: (s: Record<string, unknown>): PaymentSuccessSearch => ({
		paymentId: typeof s["paymentId"] === "string" ? s["paymentId"] : undefined,
		plan: typeof s["plan"] === "string" ? s["plan"] : undefined,
		amount: s["amount"] != null ? Number(s["amount"]) : undefined,
		discount: s["discount"] != null ? Number(s["discount"]) : undefined,
		total: s["total"] != null ? Number(s["total"]) : undefined,
		orderId: typeof s["orderId"] === "string" ? s["orderId"] : undefined,
		currency: typeof s["currency"] === "string" ? s["currency"] : "PKR",
		ts: typeof s["ts"] === "string" ? s["ts"] : undefined,
	}),
});
/** Build a typed route API for this path */
const paymentSuccessRoute = getRouteApi("/(app)/_pricing/payment-success");

function PaymentSuccess() {
	const navigate = useNavigate();
	// Typed, no-arg hook for this route’s search params
	const q = paymentSuccessRoute.useSearch() as PaymentSuccessSearch;
	const { user } = useAuthStore();

	// paymentId is required to view this page
	const paymentId = q.paymentId;

	// If there's no paymentId, redirect away
	useEffect(() => {
		if (!paymentId) {
			navigate({ to: "/selectplan" });
		}
	}, [paymentId, navigate]);

	// Fetch order details
	const { data: orderRes, isLoading, isError } = useQuery({
		queryKey: ["order", paymentId],
		queryFn: () => getOrder(paymentId as string),
		enabled: !!paymentId,
	});

	const order = orderRes?.data?.data;

	if (!paymentId || isLoading) {
		return (
			<div className="min-h-screen flex items-center justify-center">Loading...</div>
		);
	}

	if (isError || !order) {
		// Redirect back to plan selection on error
		useEffect(() => {
			navigate({ to: "/selectplan" });
		}, [navigate]);

		return null;
	}

	const now = order.created_at ? new Date(order.created_at) : new Date();
	const date = now.toLocaleDateString();
	const time = now.toLocaleTimeString([], {
		hour: "2-digit",
		minute: "2-digit",
	});

	const plan = q.plan ?? (order.subscription_days ? `${order.subscription_days} Days` : "Custom Plan");
	const amount = order.original_amount ?? order.amount ?? +(q.amount ?? 0);
	const discount = order.discount?.discount_amount ?? 0;
	const total = order.amount ?? amount - discount;
	const orderId = order.order_id ?? q.orderId ?? "xxxxx";
	const currency = q.currency ?? "PKR";

	return (
		<div className="min-h-screen font-['Inter',sans-serif] bg-gradient-to-r from-[#CBBBFF] via-[#CBBBFF20] to-transparent">
			{/* Top bar with logo on the far left (outside the card) */}
			<div className="max-w-6xl mx-auto px-6 pt-6">
				{/* Update src to your actual logo path */}
				<img
					src={ICONS.logoexpanded}
					alt="Logo"
					className="w-32 sm:w-36 lg:w-40"
				/>
			</div>

			{/* Centered content card */}
			<div className="max-w-5xl mx-auto px-6 pb-10">
				<div className="mt-8 rounded-2xl bg-white shadow-[0_20px_60px_-20px_rgba(17,24,39,0.2)]">
					{/* Back button row */}
					<div className="px-6 sm:px-8 pt-6">
						<button
							onClick={() => navigate({ to: "/selectplan" })}
							className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-800"
						>
							<ArrowLeft size={18} /> Back
						</button>
					</div>

					{/* Heading + subcopy */}
					<div className="text-center px-6 sm:px-8 pt-2">
						<h1 className="text-[32px] sm:text-[40px] font-extrabold text-[#111827]">
							Payment Success!
						</h1>
						<p className="text-sm text-gray-600 mt-1">
							You have subscribed to Parhlai Premium.
						</p>

						<p className="text-[15px] text-[#4B5563] mt-6 leading-relaxed">
							Hi {user?.displayName ?? "User"}, you have subscribe to Parhlai Premium. Thank you
							<br className="hidden sm:block" /> for using our services
						</p>
					</div>

					{/* Receipt block */}
					<div className="px-6 sm:px-8 mt-6 flex justify-center">
						<div className="w-full sm:w-[640px] rounded-xl border border-gray-200">
							<div className="px-6 pt-5 text-sm text-gray-700 space-y-1">
								<div>{date}</div>
								<div>{time}</div>
								<div>Order ID: {orderId}</div>
								<div>Status: {order.status}</div>
								<div>Payment ID: {order.payment_id}</div>
							</div>

							<div className="mt-4 px-6">
								<div className="grid grid-cols-4 text-[13px] text-gray-500 border-b border-gray-200 pb-3">
									<span>Plan</span>
									<span className="text-right">Amount</span>
									<span className="text-right">Discount</span>
									<span className="text-right">Total Amount</span>
								</div>

								<div className="grid grid-cols-4 py-3 text-[15px] text-[#111827]">
									<span className="font-medium">{plan}</span>
									<span className="text-right">{amount}</span>
									<span className="text-right">{discount}</span>
									<span className="text-right font-semibold">
										{total} {currency}
									</span>
								</div>
							</div>

							<div className="px-6 pb-5 pt-1 text-[12px] text-gray-500">
								Please keep this receipt as a record of your payment.
							</div>
						</div>
					</div>

					{/* Buttons */}
					<div className="px-6 sm:px-8 pt-6 pb-8">
						<div className="flex flex-col sm:flex-row gap-3 justify-center">
							<button
								onClick={() => navigate({ to: "/dashboard" })}
								className="h-11 px-6 rounded-xl text-white font-medium shadow-sm"
								style={{
									background: "linear-gradient(180deg,#4338CA 0%,#4338CA 100%)",
								}}
							>
								Continue Using Parhlai
							</button>

							<button
								onClick={() => {
									if (navigator.share) {
										navigator
											.share({
												title: "Parhlai Premium",
												text: `I subscribed to Parhlai Premium. Order: ${orderId}.`,
											})
											.catch(() => {});
									} else {
										window.print();
									}
								}}
								className="h-11 px-6 rounded-xl font-medium text-white shadow-sm"
								style={{
									background: "linear-gradient(180deg,#4338CA 0%,#4338CA 100%)",
								}}
							>
								Share
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
export default PaymentSuccess;

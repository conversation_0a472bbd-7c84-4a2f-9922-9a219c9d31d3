import * as React from "react";
import type { SelectedTopic, TopicFormData } from "@/features/tests/custom";

type ComboMaps = Record<string, Record<string, number>>;

const getMaxForPair = (
	comboMaps: ComboMaps,
	subjectId: string,
	topic: string,
	type: string,
	difficulty: string
): number => {
	const cm = comboMaps[subjectId] || {};
	const k = `${topic.toLowerCase()}__${type.toLowerCase()}__${difficulty.toLowerCase()}`;
	return cm[k] ?? 0;
};

// ✅ Always return a *complete* TopicFormData object
const ensureForm = (f?: TopicFormData): TopicFormData => ({
	numQuestions: f?.numQuestions ?? "",
	testType: f?.testType ?? "",
	difficulty: f?.difficulty ?? "",
});

type QuestionsInputProps = {
	subjectId: string;
	topicName: string;
	topicKey: string;

	formData: TopicFormData;
	setTopicFormData: React.Dispatch<
		React.SetStateAction<Record<string, TopicFormData>>
	>;

	selectedTopics: SelectedTopic[];
	comboMaps: ComboMaps;

	cap?: number;
	className?: string;
};

const QuestionsInput: React.FC<QuestionsInputProps> = ({
	subjectId,
	topicName,
	topicKey,
	formData,
	setTopicFormData,
	selectedTopics,
	comboMaps,
	cap = 200,
	className = "",
}) => {
	const totalSelectedMCQs = React.useMemo(
		() =>
			selectedTopics.reduce(
				(acc, t) => acc + (parseInt(t.numQuestions, 10) || 0),
				0
			),
		[selectedTopics]
	);

	const pairMax =
		formData.testType && formData.difficulty
			? getMaxForPair(
					comboMaps,
					subjectId,
					topicName,
					formData.testType,
					formData.difficulty
				)
			: 0;

	const remainingGlobal = Math.max(0, cap - totalSelectedMCQs);
	const effectiveMax = Math.min(pairMax, remainingGlobal);

	const setNumQuestions = (val: string) => {
		setTopicFormData((prev) => {
			const prevFull = ensureForm(prev[topicKey]); // <- ensure full shape
			return {
				...prev,
				[topicKey]: { ...prevFull, numQuestions: val },
			};
		});
	};

	const handleChange = (raw: string) => {
		if (raw === "") {
			setNumQuestions("");
			return;
		}

		let num = parseInt(raw, 10) || 0;

		if (formData.testType && formData.difficulty) {
			if (num > effectiveMax) num = effectiveMax;
			if (num < 1) num = 1;
		} else {
			if (num < 1) num = 1;
		}

		setNumQuestions(String(num));
	};

	const disabled =
		formData.testType && formData.difficulty ? effectiveMax === 0 : false;

	return (
		<div className={`${className}`}>
			<div className="flex flex-col">
				<div className="border rounded-md overflow-hidden w-full">
					<input
						type="number"
						min={1}
						{...(formData.testType && formData.difficulty
							? { max: effectiveMax }
							: {})}
						disabled={disabled}
						value={formData.numQuestions || ""}
						onChange={(e) => handleChange(e.target.value)}
						placeholder="No. Of Questions"
						className="w-full px-2 py-2 text-sm text-center outline-none border-0 placeholder-gray-500 disabled:bg-gray-50 disabled:text-gray-400"
						style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
					/>
				</div>

				{formData.testType && formData.difficulty ? (
					effectiveMax > 0 ? (
						<span className="text-xs text-gray-500 mt-1 text-center">
							Max: {effectiveMax}
						</span>
					) : (
						<span className="text-xs text-red-500 mt-1 text-center">
							Max: 0
						</span>
					)
				) : null}
			</div>
		</div>
	);
};

export default QuestionsInput;

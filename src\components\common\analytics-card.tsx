import React from "react";
import { Icon } from "react-feather";
// Temporarily disabled trend imports
// import { TrendingUp, TrendingDown } from "react-feather";
import { useNavigate } from "@tanstack/react-router";

export type ColorVariant = "green" | "red" | "blue";

export interface AnalyticsCardProps {
	heading: string;
	number: string | number;
	icon: Icon | React.ComponentType<{ size?: number; className?: string }>;
	helperText: string;
	colorVariant: ColorVariant;
	trendDirection?: "up" | "down";
	trendPercentage?: string;
	className?: string;
	onClick?: () => void;
}

const colorConfig = {
	green: {
		background:
			"bg-gradient-to-r from-[rgba(116,206,206,0.15)] to-[rgba(7,112,112,0.15)]",
		iconColor: "text-[#51A3A3]",
		trendColor: "text-[#04F06A]",
	},
	red: {
		background:
			"bg-gradient-to-r from-[rgba(243,61,85,0.15)] to-[rgba(255,5,38,0.15)]",
		iconColor: "text-[#D7263D]",
		trendColor: "text-[#D7263D]",
	},
	blue: {
		background:
			"bg-gradient-to-r from-[rgba(134,98,255,0.15)] to-[rgba(70,41,166,0.15)]",
		iconColor: "text-accent",
		trendColor: "text-accent",
	},
};

export const AnalyticsCard: React.FC<AnalyticsCardProps> = ({
	heading,
	number,
	icon: Icon,
	// helperText,
	colorVariant,
	// trendDirection,
	// trendPercentage,
	className = "",
	onClick,
}) => {
	const navigate = useNavigate();
	const config = colorConfig[colorVariant];
	// Temporarily disabled trend functionality
	// const TrendIcon = trendDirection === "up" ? TrendingUp : TrendingDown;
	// const trendColorClass =
	// 	trendDirection === "up" ? "text-[#04F06A]" : "text-[#D7263D]";

	const handleClick = () => {
		if (onClick) {
			onClick();
		} else {
			navigate({ to: "/analytics" });
		}
	};

	return (
		<div
			className={`rounded-[14px] p-6 ${config.background} ${className} cursor-pointer hover:opacity-90 transition-opacity`}
			onClick={handleClick}
		>
			<div className="flex justify-between items-start">
				<div>
					<p className="text-[#475569] opacity-70 font-semibold mb-4">
						{heading}
					</p>
					<p className="text-[28px] font-bold text-[#1A1C1E]">
						{typeof number === "number" ? Number(number.toFixed(2)) : number}
					</p>
				</div>
				<div className="w-[60px] h-[60px] rounded-[23px] bg-white flex items-center justify-center relative">
					<Icon size={28} className={config.iconColor} />
				</div>
			</div>
			{/* Temporarily disabled helper text section */}
			{/* <div className="flex items-center mt-6">
				<p className="text-gray-700 font-semibold">{helperText}</p>
			</div> */}
		</div>
	);
};

export default AnalyticsCard;

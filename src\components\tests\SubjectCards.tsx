import * as React from "react";
import {
	ChevronDown,
	ChevronRight,
	BookOpen,
	Atom,
	Calculator,
	FlaskConical,
	Microscope,
	Cpu,
	PenTool,
	Landmark,
	Globe,
	LineChart,
	Puzzle,
	ClipboardList,
	X,
} from "lucide-react";

import TopicItems from "@/components/tests/TopicItems";
import TopicsSearchBar from "@/components/tests/TopicSearchBar";
import MobileTopics from "@/components/tests/MobileTopics";

import type {
	Subject,
	SelectedTopic,
	TopicFormData,
} from "@/features/tests/custom";

const slug = (s: string) =>
	s
		.toLowerCase()
		.replace(/[^a-z0-9]+/g, "-")
		.replace(/(^-|-$)/g, "");
const norm = (s: string) => (s ?? "").trim().toLowerCase();

const ICON_MAP: Record<string, React.ComponentType<any>> = {
	physics: Atom,
	mathematics: Calculator,
	chemistry: FlaskConical,
	biology: Microscope,
	english: <PERSON><PERSON><PERSON>,
	"computer-science": <PERSON>pu,
	drawing: PenTool,
	"drawing-&-design": PenTool,
	history: Landmark,
	geography: Globe,
	economics: Line<PERSON>hart,
	analytical: Puzzle,
	"subject-test": ClipboardList,
};

type ComboMaps = Record<string, Record<string, number>>;

type SubjectCardsProps = {
	mode: "custom" | "subject";
	subjects: Subject[];
	selectedTopics: SelectedTopic[];
	setSelectedTopics: React.Dispatch<React.SetStateAction<SelectedTopic[]>>;
	comboMaps: ComboMaps;
	setComboMaps: React.Dispatch<React.SetStateAction<ComboMaps>>;
	topicFormData: Record<string, TopicFormData>;
	setTopicFormData: React.Dispatch<
		React.SetStateAction<Record<string, TopicFormData>>
	>;
	totalSelectedMCQs: number;
	subjectDefaults?: {
		testType: string;
		difficulty: string;
		numQuestions: string;
	};
};

function useIsMobile(breakpoint = 640) {
	const [isMobile, setIsMobile] = React.useState(false);
	React.useEffect(() => {
		if (typeof window === "undefined") return;
		const mq = window.matchMedia(`(max-width: ${breakpoint - 1}px)`);
		const update = () => setIsMobile(mq.matches);
		update();

		if (mq.addEventListener) mq.addEventListener("change", update);
		else mq.addListener(update);
		return () => {
			if (mq.removeEventListener) mq.removeEventListener("change", update);
			else mq.removeListener(update);
		};
	}, [breakpoint]);
	return isMobile;
}

const SubjectCards: React.FC<SubjectCardsProps> = ({
	mode,
	subjects,
	selectedTopics,
	setSelectedTopics,
	comboMaps,
	setComboMaps,
	topicFormData,
	setTopicFormData,
	totalSelectedMCQs,
	subjectDefaults,
}) => {
	const [expandedId, setExpandedId] = React.useState<string | null>(null);
	const [searchTerms, setSearchTerms] = React.useState<Record<string, string>>(
		{}
	);
	const [mobileSheet, setMobileSheet] = React.useState<{
		id: string;
		name: string;
	} | null>(null);

	const isMobile = useIsMobile();

	const toggleSubjectExpansion = (subjectId: string) =>
		setExpandedId((prev) => (prev === subjectId ? null : subjectId));

	const getSelectedTopicsForSubject = (subjectId: string) =>
		selectedTopics.filter((t) => t.subjectId === subjectId);

	const isPairAlreadySelected = (
		subjectId: string,
		topic: string,
		type: string,
		difficulty: string
	) =>
		selectedTopics.some(
			(t) =>
				norm(t.subjectId) === norm(subjectId) &&
				norm(t.name) === norm(topic) &&
				norm(t.testType) === norm(type) &&
				norm(t.difficulty) === norm(difficulty)
		);

	const getSelectedPairsSet = (subjectId: string, topic: string) => {
		const set = new Set<string>();
		const subj = norm(subjectId);
		const top = norm(topic);
		for (const t of selectedTopics) {
			if (norm(t.subjectId) === subj && norm(t.name) === top) {
				set.add(`${norm(t.testType)}__${norm(t.difficulty)}`);
			}
		}
		return set;
	};

	const getAllTypesForTopic = (subjectId: string, topic: string) => {
		const cm = comboMaps[subjectId] || {};
		const topicLc = topic.toLowerCase();
		const set = new Set<string>();
		for (const [key, count] of Object.entries(cm)) {
			if (!count) continue;
			const [t, type] = key.split("__");
			if (t === topicLc && type) set.add(type);
		}
		return Array.from(set).sort();
	};

	const getAllDiffsForTopic = (subjectId: string, topic: string) => {
		const cm = comboMaps[subjectId] || {};
		const topicLc = topic.toLowerCase();
		const set = new Set<string>();
		for (const [key, count] of Object.entries(cm)) {
			if (!count) continue;
			const [t, , diff] = key.split("__");
			if (t === topicLc && diff) set.add(diff);
		}
		return Array.from(set).sort();
	};

	const getTypesForTopicHavingDifficulty = (
		subjectId: string,
		topic: string,
		difficulty: string
	) => {
		const cm = comboMaps[subjectId] || {};
		const topicLc = topic.toLowerCase();
		const diffLc = difficulty.toLowerCase();
		const set = new Set<string>();
		for (const [key, count] of Object.entries(cm)) {
			if (!count) continue;
			const [t, type, diff] = key.split("__");
			if (t === topicLc && diff === diffLc && type) set.add(type);
		}
		return Array.from(set).sort();
	};

	const getDiffsForTopicType = (
		subjectId: string,
		topic: string,
		type: string
	) => {
		const cm = comboMaps[subjectId] || {};
		const topicLc = topic.toLowerCase();
		const typeLc = type.toLowerCase();
		const set = new Set<string>();
		for (const [key, count] of Object.entries(cm)) {
			if (!count) continue;
			const [t, ty, diff] = key.split("__");
			if (t === topicLc && ty === typeLc && diff) set.add(diff);
		}
		return Array.from(set).sort();
	};

	const getMaxForPair = (
		subjectId: string,
		topic: string,
		type: string,
		difficulty: string
	) => {
		const cm = comboMaps[subjectId] || {};
		const k = `${topic.toLowerCase()}__${type.toLowerCase()}__${difficulty.toLowerCase()}`;
		return cm[k] ?? 0;
	};

	const removeSelectedTopic = (topicId: string) => {
		const topicToRemove = selectedTopics.find((t) => t.id === topicId);
		if (!topicToRemove) return;

		setSelectedTopics((prev) => prev.filter((t) => t.id !== topicId));

		setComboMaps((prev) => {
			const next = { ...prev };
			const cm = { ...(next[topicToRemove.subjectId] || {}) };
			const k = `${topicToRemove.name.toLowerCase()}__${topicToRemove.testType.toLowerCase()}__${topicToRemove.difficulty.toLowerCase()}`;
			cm[k] = (cm[k] ?? 0) + (parseInt(topicToRemove.numQuestions, 10) || 0);
			next[topicToRemove.subjectId] = cm;
			return next;
		});
	};

	const onHeaderClick = (subjectId: string, subjectName: string) => {
		if (isMobile) {
			setMobileSheet({ id: subjectId, name: subjectName });
		} else {
			toggleSubjectExpansion(subjectId);
		}
	};

	return (
		<div className="space-y-6">
			{subjects.map((subject) => {
				const subjectSlug = slug(subject.name);
				const Icon =
					ICON_MAP[subjectSlug] ||
					ICON_MAP[subjectSlug.replace(/-and-/g, "-&-")] ||
					undefined;

				const allTopicsForSubject = subject.topics ?? [];
				const currentSearchTerm = searchTerms[subject.id] || "";
				const availableTopics = currentSearchTerm
					? allTopicsForSubject.filter((t) =>
							t.toLowerCase().includes(currentSearchTerm.toLowerCase())
						)
					: allTopicsForSubject;

				const selectedForThis = getSelectedTopicsForSubject(subject.id);
				const selectedNames = Array.from(
					new Set(selectedForThis.map((t) => t.name))
				);
				const summaryText = selectedNames.join(", ");
				const uniqueTopicCount = new Set(
					selectedTopics
						.filter((t) => t.subjectId === subject.id)
						.map((t) => norm(t.name))
				).size;

				const expanded = expandedId === subject.id;
				const typeLabel = (v: string) =>
					v?.toLowerCase() === "roha" ? "cramming" : v;

				return (
					<div
						key={subject.id}
						className="bg-white rounded-2xl border border-gray-200 shadow-sm"
					>
						<div
							className="px-5 py-4 flex items-start sm:items-center justify-between gap-3 border-b border-gray-100 cursor-pointer"
							role="button"
							tabIndex={0}
							onClick={() => onHeaderClick(subject.id, subject.name)}
							onKeyDown={(e) => {
								if (e.key === "Enter" || e.key === " ") {
									e.preventDefault();
									onHeaderClick(subject.id, subject.name);
								}
							}}
						>
							<div className="flex items-center gap-3 sm:gap-4 flex-1 min-w-0">
								<div className="w-11 h-11 sm:w-12 sm:h-12 rounded-xl flex items-center justify-center bg-[#EAE5F7]">
									{Icon ? (
										<Icon className="w-6 h-6 sm:w-7 sm:h-7 text-[#5936CD]" />
									) : (
										<BookOpen className="w-6 h-6 sm:w-7 sm:h-7 text-[#5936CD]" />
									)}
								</div>

								<div className="min-w-0">
									<h3
										className="text-lg text-gray-900 flex items-baseline gap-2 flex-wrap"
										style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
									>
										{subject.name}
										{uniqueTopicCount > 0 && <span> ({uniqueTopicCount})</span>}
									</h3>

									{selectedNames.length > 0 && (
										<p
											className="text-sm italic text-gray-500 mt-0.5 overflow-hidden"
											style={{
												fontFamily: "Inter, sans-serif",
												fontWeight: 400,
												display: "-webkit-box",
												WebkitBoxOrient: "vertical",
												WebkitLineClamp: 3,
											}}
											title={summaryText}
										>
											({summaryText})
										</p>
									)}
								</div>
							</div>

							<div className="shrink-0 flex items-center">
								<button
									className="hidden sm:inline-flex shrink-0 font-inter font-medium text-xs sm:text-sm leading-5 -mr-1 sm:-mr-2 text-gray-400 hover:text-gray-600 transition-colors duration-300"
									onClick={(e) => {
										e.stopPropagation();
										toggleSubjectExpansion(subject.id);
									}}
								>
									<span className="hidden sm:block">View Topics</span>
									<ChevronDown
										className={`ml-1 w-3 h-3 transition-transform duration-500 ease-in-out ${
											expanded ? "rotate-180" : ""
										}`}
									/>
								</button>

								<button
									aria-label="Open subject"
									className="sm:hidden p-2 -mr-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
									onClick={(e) => {
										e.stopPropagation();
										setMobileSheet({ id: subject.id, name: subject.name });
									}}
								>
									<ChevronRight className="w-5 h-5" />
								</button>
							</div>
						</div>

						<div
							className={`overflow-hidden transition-all duration-500 ease-in-out ${
								expanded ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0"
							}`}
						>
							<div className="px-5 pt-4 pb-5 space-y-4">
								{mode === "custom" && selectedForThis.length > 0 && (
									<div className="mb-4">
										<h5
											className="mb-2 text-gray-700"
											style={{
												fontFamily: "Inter, sans-serif",
												fontWeight: 500,
											}}
										>
											Selected Topics
										</h5>

										<div className="space-y-2">
											{selectedForThis.map((topic) => (
												<div
													key={topic.id}
													className="flex items-center justify-between gap-4 p-3 bg-white border border-gray-200 rounded-lg"
												>
													<div className="flex items-center gap-3">
														<span
															className="text-gray-900"
															style={{
																fontFamily: "Inter, sans-serif",
																fontWeight: 500,
															}}
														>
															{topic.name}
														</span>
													</div>

													<div className="flex items-center gap-3 min-w-0">
														<div className="flex items-center gap-2 w-20">
															<div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
																<svg
																	className="w-3 h-3 text-white"
																	fill="none"
																	stroke="currentColor"
																	viewBox="0 0 24 24"
																>
																	<path
																		strokeLinecap="round"
																		strokeLinejoin="round"
																		strokeWidth={2}
																		d="M5 13l4 4L19 7"
																	/>
																</svg>
															</div>
															<span
																className="text-sm text-green-600"
																style={{
																	fontFamily: "Inter, sans-serif",
																	fontWeight: 500,
																}}
															>
																Added
															</span>
														</div>

														<div className="w-24 text-sm font-medium text-gray-700">
															{topic.numQuestions} questions
														</div>

														<div className="h-4 w-px bg-gray-300" />

														<div className="w-20 text-center">
															<span
																className="text-sm text-gray-600"
																style={{
																	fontFamily: "Inter, sans-serif",
																	fontWeight: 500,
																}}
															>
																{typeLabel(topic.testType)}
															</span>
														</div>

														<div className="h-4 w-px bg-gray-300" />

														<div className="w-16 text-center">
															<span
																className="text-sm text-gray-600"
																style={{
																	fontFamily: "Inter, sans-serif",
																	fontWeight: 500,
																}}
															>
																{topic.difficulty}
															</span>
														</div>

														<div className="w-8 flex justify-center">
															<button
																onClick={() => removeSelectedTopic(topic.id)}
																className="p-1 hover:bg-gray-100 rounded-full"
															>
																<X className="w-4 h-4 text-gray-500" />
															</button>
														</div>
													</div>
												</div>
											))}
										</div>
									</div>
								)}

								<div className="flex items-center justify-between">
									<h6
										className="text-gray-900"
										style={{
											fontFamily: '"Plus Jakarta Sans", sans-serif',
											fontWeight: 500,
										}}
									>
										Chapters
									</h6>
								</div>

								<TopicsSearchBar
									value={searchTerms[subject.id] || ""}
									onChange={(v) =>
										setSearchTerms((prev) => ({ ...prev, [subject.id]: v }))
									}
									placeholder={`Search in ${subject.name}`}
								/>

								<div
									className="text-sm text-gray-500 mb-4"
									style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
								>
									{searchTerms[subject.id]
										? "Search Results"
										: "Available Topics"}
								</div>

								<TopicItems
									mode={mode}
									subjectId={subject.id}
									subjectName={subject.name}
									topics={availableTopics}
									searchTerm={searchTerms[subject.id] || ""}
									selectedTopics={selectedTopics}
									setSelectedTopics={setSelectedTopics}
									comboMaps={comboMaps}
									setComboMaps={setComboMaps}
									topicFormData={topicFormData}
									setTopicFormData={setTopicFormData}
									isPairAlreadySelected={isPairAlreadySelected}
									getSelectedPairsSet={getSelectedPairsSet}
									getAllTypesForTopic={getAllTypesForTopic}
									getAllDiffsForTopic={getAllDiffsForTopic}
									getTypesForTopicHavingDifficulty={
										getTypesForTopicHavingDifficulty
									}
									getDiffsForTopicType={getDiffsForTopicType}
									getMaxForPair={getMaxForPair}
									totalSelectedMCQs={totalSelectedMCQs}
									subjectDefaults={subjectDefaults}
									className="space-y-1 sm:space-y-3"
								/>
							</div>
						</div>
					</div>
				);
			})}

			<MobileTopics
				open={!!mobileSheet}
				onClose={() => setMobileSheet(null)}
				mode={mode}
				subjectId={mobileSheet?.id ?? ""}
				subjectName={mobileSheet?.name ?? ""}
				topics={
					mobileSheet
						? (subjects.find((s) => s.id === mobileSheet.id)?.topics ?? [])
						: []
				}
				selectedTopics={selectedTopics}
				setSelectedTopics={setSelectedTopics}
				comboMaps={comboMaps}
				setComboMaps={setComboMaps}
				topicFormData={topicFormData}
				setTopicFormData={setTopicFormData}
				totalSelectedMCQs={totalSelectedMCQs}
				isPairAlreadySelected={isPairAlreadySelected}
				getSelectedPairsSet={getSelectedPairsSet}
				getAllTypesForTopic={getAllTypesForTopic}
				getAllDiffsForTopic={getAllDiffsForTopic}
				getTypesForTopicHavingDifficulty={getTypesForTopicHavingDifficulty}
				getDiffsForTopicType={getDiffsForTopicType}
				getMaxForPair={getMaxForPair}
			/>
		</div>
	);
};

export default SubjectCards;

import * as React from "react";
import { Button } from "@/components/ui/button";
import { MenuDropdown } from "@/components/tests/MenuDropdown";

type SubjectExtras =
	| {
			topicsCount: number;

			questionsNode?: React.ReactNode;

			questionsProps?: {
				value: string | number;
				onChange: (value: string) => void;
				placeholder?: string;
				disabled?: boolean;
				min?: number;
			};

			mode: {
				value: string;
				onChange: (value: string) => void;
				options?: string[];
			};
	  }
	| undefined;

type BottomActionsBarProps = {
	subjectExtras?: SubjectExtras;

	mcqs?: { value: number; cap: number; label?: string };
	count?: { value: number; label?: string };

	onReset: () => void;
	onAttempt: () => void;

	loading?: boolean;
	attemptDisabled?: boolean;

	className?: string;
};

const BottomActionsBar: React.FC<BottomActionsBarProps> = ({
	subjectExtras,
	mcqs,
	count,
	onReset,
	onAttempt,
	loading = false,
	attemptDisabled = false,
	className = "",
}) => {
	const isSubject = !!subjectExtras;

	return (
		<>
			{/* Desktop / large screens: original inline bar */}
			<div
				className={`hidden lg:inline-flex items-center gap-4 rounded-2xl border border-gray-200 bg-white px-3 py-2 shadow-sm w-fit ${className}`}
			>
				{isSubject ? (
					<div className="flex items-center gap-3">
						<div className="flex items-baseline gap-1 text-gray-700 px-2 py-1 rounded-md">
							<span className="font-semibold">
								{subjectExtras!.topicsCount}
							</span>
							<span className="text-sm">topics</span>
						</div>

						<span className="h-6 w-px bg-gray-200" />

						<div className="px-2 py-1 rounded-md">
							{subjectExtras!.questionsNode ? (
								subjectExtras!.questionsNode
							) : (
								<div className="flex items-center">
									<input
										type="number"
										min={subjectExtras!.questionsProps?.min ?? 1}
										disabled={subjectExtras!.questionsProps?.disabled}
										value={String(subjectExtras!.questionsProps?.value ?? "")}
										onChange={(e) =>
											subjectExtras!.questionsProps?.onChange?.(e.target.value)
										}
										placeholder={
											subjectExtras!.questionsProps?.placeholder ??
											"No. of Questions"
										}
										className="w-40 px-2 py-1 text-sm text-center outline-none border border-gray-300 rounded-md placeholder-gray-700"
										style={{ fontFamily: "Inter, sans-serif", fontWeight: 400 }}
									/>
								</div>
							)}
						</div>

						<span className="h-6 w-px bg-gray-200" />

						<div className="px-2 py-1 rounded-md">
							<MenuDropdown
								value={subjectExtras!.mode.value}
								onChange={subjectExtras!.mode.onChange}
								options={subjectExtras!.mode.options ?? ["Timed", "Untimed"]}
								placeholder="Test mode"
								menuMinWidth="10rem"
								className="w-32"
							/>
						</div>
					</div>
				) : (
					<>
						{/* Custom Test meta visible on desktop only */}
						<div className="flex items-center gap-2">
							<div className="text-sm text-gray-700">
								<div className="text-xs text-gray-500">
									{mcqs?.label ?? "Total MCQs"}
								</div>
								<div className="font-medium">
									{mcqs?.value ?? 0} / {mcqs?.cap ?? 200}
								</div>
							</div>
						</div>

						<span className="h-6 w-px bg-gray-200" />

						<div className="flex items-baseline gap-1 text-gray-700">
							<span className="font-semibold">{count?.value ?? 0}</span>
							<span className="text-sm">{count?.label ?? "chapters"}</span>
						</div>
					</>
				)}

				<span className="h-6 w-px bg-gray-200" />

				<div className="flex items-center gap-3">
					<Button
						variant="outline"
						size="sm"
						className="rounded-xl px-4 border border-gray-200 bg-gray-50 text-gray-700 hover:bg-gray-100 gap-2"
						onClick={onReset}
					>
						<svg
							className="w-4 h-4"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
						>
							<path d="M21 12a9 9 0 1 1-3.51-7.06" />
							<polyline points="21 3 21 9 15 9" />
						</svg>
						Reset
					</Button>

					<Button
						className="rounded-xl px-5 py-3 bg-[#5936cd] text-white hover:bg-[#4c2dae]"
						disabled={loading || attemptDisabled}
						onClick={onAttempt}
					>
						{loading ? (
							<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
						) : (
							<svg
								className="w-4 h-4"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
							>
								<path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" />
								<path d="M20 22H6.5a2.5 2.5 0 0 1 0-5H20z" />
								<path d="M8 6h13M8 12h13" />
							</svg>
						)}
						<span className="ml-2">Attempt</span>
					</Button>
				</div>
			</div>

			{/* Mobile */}
			<div
				className="lg:hidden fixed inset-x-0 z-50"
				style={{ bottom: "max(1.25rem, env(safe-area-inset-bottom))" }}
			>
				<div className="mx-auto max-w-sm h-20 p-2.5 grid grid-cols-2 gap-2.5 rounded-2xl shadow-xl bg-white">
					<Button
						variant="outline"
						className="rounded-xl flex h-full p-2"
						onClick={onReset}
					>
						<svg
							className="w-5 h-5"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
						>
							<path d="M21 12a9 9 0 1 1-3.51-7.06" />
							<polyline points="21 3 21 9 15 9" />
						</svg>
						<span className="ml-2">Reset</span>
					</Button>

					<Button
						className="rounded-xl flex h-full p-2 bg-[#5936cd] text-white hover:bg-[#4c2dae] disabled:bg-[#5936cd] disabled:opacity-60 focus-visible:ring-2 focus-visible:ring-[#5936cd]/50"
						disabled={loading || attemptDisabled}
						onClick={onAttempt}
					>
						{loading ? (
							<div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white" />
						) : (
							<svg
								className="w-5 h-5"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
							>
								<path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" />
								<path d="M20 22H6.5a2.5 2.5 0 0 1 0-5H20z" />
								<path d="M8 6h13M8 12h13" />
							</svg>
						)}
						<span className="ml-2">Attempt</span>
					</Button>
				</div>
			</div>

			<div className="lg:hidden h-28" aria-hidden="true" />
		</>
	);
};

export default BottomActionsBar;

import { APIResponse } from "@/lib/api/types";

// Order returned by GET /api/payments/order/{paymentId}
export interface Order {
    payment_id: string;
    order_id: string;
    amount: number;
    original_amount?: number;
    status: string;
    method: string;
    subscription_days?: number;
    valid_from?: string;
    valid_until?: string;
    created_at?: string;
    updated_at?: string;
    discount?: {
        code?: string;
        type?: string;
        value?: number;
        discount_amount?: number;
    } | null;
    user?: any | null;
}

export interface CreateOrderPayload {
    subscription_days: number;
    method: "bank_transfer";
}

export type CreateOrderRes = APIResponse<Order>;

export type ApplyDiscountPayload = {
    payment_id: string;
    discount_code: string;
};

export type DiscountInfo = {
    final_amount: number;
    discount_amount: number;
};

export type PaymentProofUploadUrl = {
    upload_url: string;
    method: string;
    image_key?: string;
    payment_id?: string;
    transaction_reference?: string;
    expires_in?: number;
};

export type ApplyDiscountRes = APIResponse<DiscountInfo>;

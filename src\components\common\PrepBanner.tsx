import * as React from "react";

type PrepBannerProps = {
	value: string;

	label?: string;

	onChangeClick?: () => void;

	changeLabel?: string;

	maxPillWidth?: number | string;

	className?: string;
};

const toPx = (v?: number | string) => (typeof v === "number" ? `${v}px` : v);

const PrepBanner: React.FC<PrepBannerProps> = ({
	value,
	label = "I'm preparing for:",
	onChangeClick,
	changeLabel = "Change",
	maxPillWidth = 360,
	className = "",
}) => {
	return (
		<div
			className={[
				"flex flex-wrap items-center justify-center gap-2 sm:gap-3",
				"text-sm font-normal",
				className,
			].join(" ")}
		>
			<span className="text-[#475569] shrink-0">{label}</span>

			<div className="border border-accent rounded-full p-1 min-w-0 max-w-full">
				<span
					title={value}
					className={[
						"inline-flex items-center text-white bg-accent rounded-full",
						"px-3 py-1 font-medium leading-5",
						"break-words whitespace-normal",
						"sm:whitespace-nowrap sm:truncate sm:text-ellipsis",
					].join(" ")}
					style={{ maxWidth: toPx(maxPillWidth) }}
				>
					{value}
				</span>
			</div>

			{onChangeClick && (
				<button
					onClick={onChangeClick}
					className="text-sm text-accent underline underline-offset-[4px] hover:no-underline transition-all duration-200 shrink-0 whitespace-nowrap"
					type="button"
				>
					{changeLabel}
				</button>
			)}
		</div>
	);
};

export default PrepBanner;

import * as React from "react";
import { Search, X } from "lucide-react";
import type { MockTest } from "@/features/tests/custom.api";

type Props = {
	open: boolean;
	tests: MockTest[];
	selectedPrep: string;
	loading: boolean;
	error: string | null;
	onClose: () => void;
	onSelect: (testId: string) => void;
};

const TestSelectionModal: React.FC<Props> = ({
	open,
	tests,
	selectedPrep,
	loading,
	error,
	onClose,
	onSelect,
}) => {
	const [q, setQ] = React.useState("");

	const visibleTests = React.useMemo(() => {
		const query = q.trim().toLowerCase();
		if (!query) return tests;
		return tests.filter(
			(t) =>
				t.name.toLowerCase().includes(query) ||
				t.testId.toLowerCase().includes(query)
		);
	}, [tests, q]);

	if (!open) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
			<div className="bg-white rounded-2xl p-6 w-full max-w-md mx-4 shadow-xl">
				<div className="flex items-center justify-between mb-6">
					<h3 className="text-lg font-semibold text-gray-900">Select Test</h3>
					<button
						onClick={onClose}
						className="p-1 hover:bg-gray-100 rounded-full"
					>
						<X className="w-5 h-5 text-gray-500" />
					</button>
				</div>

				<div className="relative mb-4">
					<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
					<input
						type="text"
						value={q}
						onChange={(e) => setQ(e.target.value)}
						placeholder="Search Test"
						className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					/>
				</div>

				<div className="space-y-2 max-h-80 overflow-y-auto">
					{loading ? (
						<div className="px-4 py-6 text-sm text-gray-600 text-center">
							Loading tests…
						</div>
					) : error ? (
						<div className="px-4 py-6 text-sm text-red-600 text-center">
							{error}
						</div>
					) : visibleTests.length === 0 ? (
						<div className="px-4 py-6 text-sm text-gray-500 text-center">
							No tests match “{q}”
						</div>
					) : (
						visibleTests.map((t) => (
							<button
								key={t.testId}
								onClick={() => onSelect(t.testId)}
								className={`w-full text-left px-4 py-3 rounded-xl text-sm font-medium transition-colors ${
									selectedPrep === t.testId
										? "bg-blue-50 text-blue-700 border-2 border-blue-200"
										: "bg-gray-50 text-gray-700 hover:bg-gray-100 border-2 border-transparent"
								}`}
								title={t.description}
							>
								{t.name}
							</button>
						))
					)}
				</div>
			</div>
		</div>
	);
};

export default TestSelectionModal;

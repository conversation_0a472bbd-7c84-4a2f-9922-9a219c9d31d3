import { LucideIcon } from 'lucide-react';

export interface Subject {
  id: string;
  name: string;
  count: number;       
  topics: string[];    
  icon?: LucideIcon;

  // populated from backend:
  availableCount?: number;           
  availableTypes?: string[];            
  availableDifficulties?: string[];    
  topicTotals?: Record<string, number>; 

  comboMap?: Record<string, number>;    
}

export interface SubjectConfig {
  subjects: Subject[];
}

// export interface SubjectConfigs {
//   [key: string]: SubjectConfig;
// }

export interface SelectedTopic {
  id: string;
  subjectId: string;
  name: string;
  numQuestions: string;
  testType: string;
  difficulty: string;
}

export interface TopicFormData {
  numQuestions: string;
  testType: string;
  difficulty: string;
}

export interface ToggleSubjectExpansion {
  (subjectId: string): void;
}

export interface UpdateSubjectTopics {
  (subjectId: string, newTopics: string[]): void;
}

export interface AddTopic {
  (subjectId: string): void;
}

// export interface AddVariant {
//   (subjectId: string, topicName: string, formData: TopicFormData): void;
// }

export interface RemoveSelectedTopic {
  (topicId: string): void;
}
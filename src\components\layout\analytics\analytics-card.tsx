import React from "react";

interface AnalyticsCardProps {
	title: string;
	value: string | number;
	subtitle: string;
	children: React.ReactNode;
	className?: string;
	showTotal?: boolean;
	onShowTotal?: () => void;
}

export const AnalyticsCard = ({
	title,
	value,
	subtitle,
	children,
	className = "",
	// showTotal = false,
	// onShowTotal,
}: AnalyticsCardProps) => (
	<div
		className={`mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300 ${className}`}
	>
		<div className="mb-6">
			<h3 className="text-sm font-bold text-gray-400 mb-2.5">{title}</h3>
			<p className="text-[32px] font-medium text-gray-700">
				{value}{" "}
				<span className="text-base font-medium text-gray-400">{subtitle}</span>
			</p>
			{/* {showTotal && onShowTotal && (
				<button
					onClick={onShowTotal}
					className="text-accent text-sm mt-2 underline hover:no-underline transition-all duration-200"
				>
					Show Total
				</button>
			)} */}
		</div>
		{children}
	</div>
);

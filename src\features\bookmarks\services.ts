import { api } from "@/lib/api";
import {
	addBookmarkReq,
	getBookmarksReq,
	getBookmarksResp,
} from "./types";

export const getBookmarksList = <C extends keyof getBookmarksResp>(
	params: getBookmarksReq<C>
) => {
	const { category, page, limit } = params;
	return api.get<getBookmarksResp[C]>(
		`bookmark/get?category=${category}&page=${page}&limit=${limit}`
	);
	// return api.get(
	// 	`bookmark/get?category=${category}&page=${page}&limit=${limit}`
	// );
};

export const addBookmark = (data: addBookmarkReq) => {
	return api.patch(`bookmark/add`, data);
};

export const deleteBookmark = (data: addBookmarkReq) => {
	return api.delete(`bookmark/remove`, { data });
};
